import { defineConfig } from "cypress";
import { cypressBrowserPermissionsPlugin } from 'cypress-browser-permissions';
import vitePreprocessor from 'cypress-vite';
import protobufjs from 'protobufjs';

// @ts-ignore
import protobufEncode from 'cypress-protobuf';

export default defineConfig({
  projectId: "j5xw8i",
  video: false,
  screenshotOnRunFailure: false,
  experimentalInteractiveRunEvents: true,
  env: {
    "browserPermissions": {
      "notifications": "allow",
      "geolocation": "allow",
      "camera": "block",
      "microphone": "block",
      "images": "allow",
      "javascript": "allow",
      "popups": "ask",
      "plugins": "ask",
      "cookies": "allow",
      "midi": "allow"
    }
  },
  chromeWebSecurity: false,
  e2e: {
    //@ts-ignore
    hideXHRInCommandLog: true,
    experimentalRunAllSpecs: true,
    baseUrl: 'http://localhost:4000',
    experimentalStudio: true,
    env: {
      "browserPermissions": {
        "notifications": "allow",
        "geolocation": "allow",
        "camera": "block",
        "microphone": "block",
        "images": "allow",
        "javascript": "allow",
        "popups": "ask",
        "plugins": "ask",
        "cookies": "allow",
        "midi": "allow"
      }
    },
    setupNodeEvents(on, config) {
      config = cypressBrowserPermissionsPlugin(on, config);

      on('file:preprocessor',
        vitePreprocessor({
          mode: 'development'
        })
      );

      on("before:browser:launch", (browser, launchOptions) => {
        if (browser.family === "chromium" || browser.family === "webkit") {
          launchOptions.args.push("--mute-audio");
          launchOptions.args.push("--disable-user-media-security");
        }
        return launchOptions;
      });

      let previousProtoFilePath: string | null = null;

      on('task', {
        protobufEncode:
          ({ fixtureBody, message, protoFilePath } = {}) => {
            if (protoFilePath) {
              previousProtoFilePath = protoFilePath;
            }

            const protoPath = previousProtoFilePath;
            if (!protoPath) {
              throw new Error("No proto file path has been provided.");
            }

            // allows to set the proto file path in advance
            if (!message || !fixtureBody) {
              return null;
            }

            const proto = protobufjs.loadSync(protoPath);
            const protoMessage = proto.lookupType(message);
            return protoMessage.encode(fixtureBody).finish();
          }
      });

      return config;
    },

    defaultCommandTimeout: 10_000,
    specPattern: "cypress/e2e/**/*.cy.{js,jsx,ts,tsx}"
  }
});
