import { defineConfig, ViteCustomizableConfig } from "@solidjs/start/config";
import { loadEnv } from 'vite';
import replace from '@rollup/plugin-replace';
import { run } from 'vite-plugin-run';
import { viteStaticCopy } from 'vite-plugin-static-copy';
import { internalIpV4 } from "internal-ip";
import * as fs from 'fs';
import appPackage from "./package.json";
import * as path from "path";
import { fileURLToPath } from 'url';

const mode = (process.env.BUILD_ENV ?? process.env.NODE_ENV ?? "development").toLowerCase();
process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };
console.log("Environment", mode);

const mobile = !!/android|ios/.exec(process.env.TAURI_ENV_PLATFORM!);

const packageVersion = appPackage.version || "0.0.0-0";
let index = packageVersion.indexOf("-");
if (index == -1) index = packageVersion.length;
const APP_VERSION = packageVersion.substring(0, index);

let DEPLOY_COMMIT_DATE: Date | null = null;
try {
  DEPLOY_COMMIT_DATE = require('./get-build-date.js').default;
} catch (e) {
  console.error("Failed to get build date", e);
}

const replaceOptions = {
  preventAssignment: true,
  __DATE__: DEPLOY_COMMIT_DATE ? DEPLOY_COMMIT_DATE.getTime() : Date.now(),
  __APP_VERSION__: APP_VERSION,
  __BUILD_ENV__: mode
};

const isStaging = mode === "staging";
const isProduction = isStaging || mode === "production" || mode == "prod";

const isTestMode = Boolean(process.env.VITEST) || Boolean(process.env.CYPRESS);
const isCI = Boolean(process.env.BUDDY || process.env.GITHUB_ACTIONS);

if (isProduction) {
  let data = fs.readFileSync('./package.json');
  let packageJsonObj = JSON.parse(data.toString());

  if (packageJsonObj) {
    packageJsonObj["build:date"] = replaceOptions.__DATE__.toString();
    packageJsonObj = JSON.stringify(packageJsonObj, null, 2);

    fs.writeFileSync('./package.json', packageJsonObj);
    console.log('The file has been saved!');
  } else {
    throw new Error("Failed to read package.json");
  }
}

console.info(`
-- Starting Build --
| Version: ${APP_VERSION}
| isProduction: ${isProduction}
| Build Date: ${replaceOptions.__DATE__}
| Mode: ${mode}
| Test: ${isTestMode}
| CI: ${isCI}
---------------------
`);

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default defineConfig({
  server: {
    // compatibilityDate: "2025-02-05",
    preset: "./preset",
    // minify: isProduction,
    minify: false,
    // sourceMap: !isProduction,
    sourceMap: true,
    "routeRules": {
      "/": {
        prerender: isProduction
      },
      "/*": {
        "cors": true,
        headers: {
          "Cross-Origin-Opener-Policy": "same-origin",
          "Cross-Origin-Embedder-Policy": "require-corp",
          "Cross-Origin-Resource-Policy": "cross-origin",
          "x-pianorhythm-client-version": APP_VERSION ?? "0.0.0"
        }
      }
    },
    prerender: {
      crawlLinks: isProduction
    }
  },
  middleware: "./src/server/middleware",
  vite: {
    // Vite options tailored for Tauri development and only applied in `tauri dev` or `tauri build`
    //
    // 1. prevent vite from obscuring rust errors
    clearScreen: false,
    envPrefix: ["VITE_", "TAURI_"],
    plugins: [
      !isProduction && !isCI && run([
        {
          name: 'build proto',
          run: [`node ${path.resolve(__dirname, "./build-protobuf-ts.cjs")}`],
          pattern: ['./pianorhythm_core/proto/raw/**.proto']
        }
      ]),
      !isTestMode && viteStaticCopy({
        targets: [
          {
            src: './pianorhythm_core/pkg/**',
            dest: 'pianorhythm_core/pkg'
          }
        ]
      }),
      replace(replaceOptions),
      !isProduction && {
        name: "configure-response-headers",
        configureServer: (server: any) => {
          server.middlewares.use((_req: any, res: any, next: any) => {
            res.setHeader("Cross-Origin-Opener-Policy", "same-origin");
            res.setHeader("Cross-Origin-Embedder-Policy", "require-corp");
            res.setHeader("Cross-Origin-Resource-Policy", "cross-origin");
            res.setHeader("x-pianorhythm-latest-client-version", APP_VERSION || "0.0.1");
            next();
          });
        }
      }
    ].filter(Boolean),
    ssr: {
      noExternal: [
        "@hope-ui/solid",
        "solid-dismiss"
      ]
    },
    server: {
      host: mobile ? "0.0.0.0" : "127.0.0.1",
      hmr: mobile
        ? {
          protocol: "ws",
          host: await internalIpV4(),
          port: 1421
        }
        : undefined,
      proxy: {
        "https://assets.pianorhythm.io": {
          target: "https://assets.pianorhythm.io",
          changeOrigin: true
        }
      },
      watch: {
        // 3. tell vite to ignore watching `src-tauri`
        ignored: ["**/pianorhythm_core/**"]
      }
    },
    worker: {
      format: "es",
      plugins: () => [
        replace(replaceOptions)
      ]
    },
    resolve: {
      alias: [{ find: "@core", replacement: "/pianorhythm_core" }]
    },
    build: {
      target: "esnext",
      minify: !process.env.TAURI_DEBUG ? "esbuild" : false,
      sourcemap: !!process.env.TAURI_DEBUG
    },
    optimizeDeps: {
      esbuildOptions: {
        target: 'esnext',
        supported: {
          bigint: true
        }
      },
      include: [
        "@hope-ui/solid",
        "lodash-es",
        "rxjs",
        "luxon",
        "animejs",
        "protobufjs",
        "sweetalert2",
        "abcjs",
        "interactjs"
      ]
    }
  } as ViteCustomizableConfig
});
