/**
 * Initialization state types for managing the app startup sequence
 */

export enum InitializationStep {
  // Core steps
  UserGesture = "user-gesture",
  CoreWasm = "core-wasm",
  AppState = "app-state",

  // Connection steps
  WebsocketIdentity = "websocket-identity",
  WebsocketConnection = "websocket-connection",
  WelcomeEvent = "welcome-event",
  ClientLoaded = "client-loaded",

  // Audio steps (refactored order)
  AudioService = "audio-service",
  SynthEngine = "synth-engine",
  ClientSocketId = "client-socket-id",
  ClientAddedToSynth = "client-added-to-synth",
  Soundfont = "soundfont",

  // Service steps (now depend on soundfont being loaded)
  AppSettings = "app-settings",
  UsersService = "users-service",
  ChatService = "chat-service",
  RoomsService = "rooms-service",
  MonitorService = "monitor-service",

  // Final step
  Complete = "complete"
}

/**
 * InitializationStepInfo - Information about a specific initialization step
 */
export interface InitializationStepInfo {
  /** The step itself */
  step: InitializationStep;
  /** The status of the step */
  status: InitializationStatus;
  /** Timestamps for timing the step */
  startTime?: number;
  /** Timestamp for when the step completed or failed */
  endTime?: number;
  /** Error message if the step failed */
  error?: string;
  /** Dependencies for the step */
  dependencies: InitializationStep[];
  /** Timeout and retry information */
  timeout?: number;
  /** Retry count and max retries */
  retryCount?: number;
  /** Maximum number of retries */
  maxRetries?: number;
}

/**
 * InitializationStatus - Status of a specific initialization step
 */
export enum InitializationStatus {
  Pending = "pending",
  InProgress = "in-progress",
  Completed = "completed",
  Failed = "failed",
  Retrying = "retrying"
}

/**
 * InitializationState - Overall state of the initialization process
 */
export interface InitializationState {
  steps: Map<InitializationStep, InitializationStepInfo>;
  currentStep?: InitializationStep;
  isComplete: boolean;
  hasErrors: boolean;
  startTime: number;
}

/**
 * InitializationConfig - Configuration for the initialization process
 */
export interface InitializationConfig {
  defaultTimeout: number;
  maxRetries: number;
  retryDelay: number;
  enableLogging: boolean;
}

/**
 * StepExecutor - Interface for executing a specific initialization step
 */
export interface StepExecutor {
  execute: () => Promise<void>;
  validate?: () => Promise<boolean>;
  cleanup?: () => Promise<void>;
}

/**
 * Callbacks for progress and errors during initialization
 */
export type InitializationProgressCallback = (step: InitializationStep, status: InitializationStatus, progress: number) => void;

/**
 * Callback for errors during initialization
 */
export type InitializationErrorCallback = (step: InitializationStep, error: string) => void;
