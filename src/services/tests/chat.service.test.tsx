import { describe, it, expect, vi } from 'vitest';
import SwalPR from '~/util/sweetalert';
import ChatService from '~/services/chat.service';
import SoundEffectsService from '~/services/sound-effects.service';
import AppSettingsService from '~/services/settings-storage.service';
import { MockAppService, MockAppSettingsService, MockEmojifyService, MockI18nService, MockSoundEffectsService, MockWebSocketService } from '@test/mocks/service.mocks';
import { ServiceGetter, useService } from 'solid-services';
import WebsocketService from '~/services/websocket.service';
import { Command } from '~/types/chat.types';
import AppService from '~/services/app.service';
import EmojifyService from '~/services/emojify.service';
import { UserClientDomain } from '~/types/user-helper';
import { UserClientDto } from '~/proto/user-renditions';
import I18nService from '../i18n.service';

vi.mock("~/util/sweetalert");

vi.mock("solid-services", () => ({
  useService: vi.fn()
}));

describe('ChatService', () => {
  let chatService: ServiceGetter<ReturnType<typeof ChatService>>;
  let mockWebsocketService: ReturnType<typeof WebsocketService>;
  let mockSoundEffectsService: ReturnType<typeof SoundEffectsService>;
  let mockAppSettingsService: ReturnType<typeof AppSettingsService>;
  let mockAppService: ReturnType<typeof AppService>;

  beforeEach(() => {
    mockSoundEffectsService = MockSoundEffectsService();
    mockAppSettingsService = MockAppSettingsService();
    mockWebsocketService = MockWebSocketService();
    mockAppService = MockAppService();

    vi.spyOn(mockAppService, 'isClientMember').mockReturnValue(true);
    (SwalPR as any).mockReturnValue({ fire: vi.fn().mockResolvedValue({ isConfirmed: false }) });

    (useService as any).mockImplementation((service: any) => {
      if (service === SoundEffectsService) return () => mockSoundEffectsService;
      if (service === AppSettingsService) return () => mockAppSettingsService;
      if (service === WebsocketService) return () => mockWebsocketService;
      if (service === AppService) return () => mockAppService;
      if (service === EmojifyService) return () => MockEmojifyService();
      if (service === I18nService) return () => MockI18nService();
    });

    let service = ChatService();
    service.initialize(
      { listen: vi.fn() } as any,
      { listen: vi.fn() } as any,
    );
    chatService = () => service;
  });

  it('should run room chat server command for members only command', async () => {
    let value = 'New profile description';
    (SwalPR as any).mockReturnValue({
      fire: vi.fn().mockResolvedValue({
        isConfirmed: true, value
      })
    });
    vi.spyOn(mockAppService, 'isClientMember').mockReturnValue(true);

    const command: Command = {
      command: 'profile_description',
      membersOnly: true,
      moduleID: ''
    };

    await chatService().runRoomChatServerCommand(command, [value]);

    expect(mockWebsocketService.emitUserUpdateCommand).toHaveBeenCalled();
  });

  it('should not run room chat server command for members only command if not a member', async () => {
    let value = 'New profile description';
    (SwalPR as any).mockReturnValue({
      fire: vi.fn().mockResolvedValue({
        isConfirmed: true, value
      })
    });
    vi.spyOn(mockAppService, 'isClientMember').mockReturnValue(false);

    const command: Command = {
      command: 'profile_description',
      membersOnly: true,
      moduleID: ''
    };

    await expect(chatService().runRoomChatServerCommand(command, [value])).rejects.toThrow();

    expect(mockWebsocketService.emitUserUpdateCommand).not.toHaveBeenCalled();
  });

  describe('Profile Description Modal', () => {
    it('should clear profile description when confirmed', async () => {
      const mockFire = vi.fn().mockResolvedValue({ isConfirmed: true });
      (SwalPR as any).mockReturnValue({ fire: mockFire });
      vi.spyOn(mockAppService, 'client').mockReturnValue(new UserClientDomain(UserClientDto.create({ userDto: { ProfileDescription: 'test' } })));

      await chatService().triggerClearProfileDescription();

      expect(mockWebsocketService.emitUserUpdateCommand).toHaveBeenCalledWith(['ProfileDescription', '']);
    });

    it('should not clear profile description when cancelled', async () => {
      const mockFire = vi.fn().mockResolvedValue({ isConfirmed: false });
      (SwalPR as any).mockReturnValue({ fire: mockFire });
      vi.spyOn(mockAppService, 'client').mockReturnValue(new UserClientDomain(UserClientDto.create({ userDto: { nickname: '' } })));

      await chatService().triggerClearProfileDescription();

      expect(mockWebsocketService.emitUserUpdateCommand).not.toHaveBeenCalled();
    });
  });

  describe('Command: Nickname', () => {
    it('should run room chat server command for nickname command', async () => {
      let value = 'New nickname';
      (SwalPR as any).mockReturnValue({
        fire: vi.fn().mockResolvedValue({
          isConfirmed: true, value
        })
      });

      const command: Command = {
        command: 'nickname',
        membersOnly: true,
        moduleID: ''
      };

      await chatService().runRoomChatServerCommand(command, [value]);

      expect(mockWebsocketService.emitUserUpdateCommand).toHaveBeenCalled();
    });

    it('should clear nickname when confirmed', async () => {
      const mockFire = vi.fn().mockResolvedValue({ isConfirmed: true });
      (SwalPR as any).mockReturnValue({ fire: mockFire });
      vi.spyOn(mockAppService, 'client').mockReturnValue(new UserClientDomain(UserClientDto.create({ userDto: { nickname: 'test' } })));

      await chatService().clearNickname();

      expect(mockWebsocketService.emitUserUpdateCommand).toHaveBeenCalledWith(['Nickname', '']);
    });

    it('should not clear nickname when client does not have a nickname', async () => {
      const mockFire = vi.fn().mockResolvedValue({ isConfirmed: true });
      (SwalPR as any).mockReturnValue({ fire: mockFire });
      vi.spyOn(mockAppService, 'client').mockReturnValue(new UserClientDomain(UserClientDto.create({ userDto: { nickname: '' } })));

      await chatService().clearNickname();

      expect(mockWebsocketService.emitUserUpdateCommand).not.toHaveBeenCalled();
    });
  });

  describe('Command: Status Text', () => {
    it('should run room chat server command for status text command', async () => {
      let value = 'New status text';
      (SwalPR as any).mockReturnValue({
        fire: vi.fn().mockResolvedValue({
          isConfirmed: true, value
        })
      });

      const command: Command = {
        command: 'status_text',
        membersOnly: true,
        moduleID: ''
      };

      await chatService().runRoomChatServerCommand(command, [value]);

      expect(mockWebsocketService.emitUserUpdateCommand).toHaveBeenCalled();
    });

    it('should clear status text when confirmed', async () => {
      const mockFire = vi.fn().mockResolvedValue({ isConfirmed: true });
      (SwalPR as any).mockReturnValue({ fire: mockFire });
      vi.spyOn(mockAppService, 'client').mockReturnValue(new UserClientDomain(UserClientDto.create({ userDto: { statusText: 'test' } })));

      await chatService().clearStatusText();

      expect(mockWebsocketService.emitUserUpdateCommand).toHaveBeenCalledWith(['StatusText', '']);
    });

    it('should not clear status text when client does not have a status text', async () => {
      const mockFire = vi.fn().mockResolvedValue({ isConfirmed: true });
      (SwalPR as any).mockReturnValue({ fire: mockFire });
      vi.spyOn(mockAppService, 'client').mockReturnValue(new UserClientDomain(UserClientDto.create({ userDto: { statusText: '' } })));

      await chatService().clearStatusText();

      expect(mockWebsocketService.emitUserUpdateCommand).not.toHaveBeenCalled();
    });
  });

  describe('Command: Profile Description', () => {
    it('should run room chat server command for profile description command', async () => {
      let value = 'New profile description';
      (SwalPR as any).mockReturnValue({
        fire: vi.fn().mockResolvedValue({
          isConfirmed: true, value
        })
      });

      const command: Command = {
        command: 'profile_description',
        membersOnly: true,
        moduleID: ''
      };

      await chatService().runRoomChatServerCommand(command, [value]);

      expect(mockWebsocketService.emitUserUpdateCommand).toHaveBeenCalled();
    });

    it('should clear profile description when confirmed', async () => {
      const mockFire = vi.fn().mockResolvedValue({ isConfirmed: true });
      (SwalPR as any).mockReturnValue({ fire: mockFire });
      vi.spyOn(mockAppService, 'client').mockReturnValue(new UserClientDomain(UserClientDto.create({ userDto: { ProfileDescription: 'test' } })));

      await chatService().clearProfileDescription();

      expect(mockWebsocketService.emitUserUpdateCommand).toHaveBeenCalledWith(['ProfileDescription', '']);
    });

    it('should not clear profile description when client does not have a profile description', async () => {
      const mockFire = vi.fn().mockResolvedValue({ isConfirmed: true });
      (SwalPR as any).mockReturnValue({ fire: mockFire });
      vi.spyOn(mockAppService, 'client').mockReturnValue(new UserClientDomain(UserClientDto.create({ userDto: { ProfileDescription: '' } })));

      await chatService().clearProfileDescription();

      expect(mockWebsocketService.emitUserUpdateCommand).not.toHaveBeenCalled();
    });
  });
});