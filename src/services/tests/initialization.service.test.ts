import { describe, it, expect, beforeEach, vi } from 'vitest';
import { createRoot, createSignal } from 'solid-js';
import { InitializationStep, InitializationStatus } from '~/types/initialization.types';

// Mock the logger
vi.mock('~/util/logger', () => ({
  logDebug: vi.fn(),
  logError: vi.fn(),
  logInfo: vi.fn()
}));

// Mock the helpers
vi.mock('~/util/helpers', () => ({
  until: vi.fn((condition) => {
    return new Promise((resolve) => {
      const check = () => {
        if (condition()) {
          resolve(true);
        } else {
          setTimeout(check, 10);
        }
      };
      check();
    });
  })
}));

// Mock solid-services
vi.mock('solid-services', () => ({
  useService: vi.fn()
}));

describe('InitializationService', () => {
  let initializationService: any;

  beforeEach(async () => {
    // Dynamic import to avoid module loading issues
    const { default: InitializationService } = await import('./initialization.service');
    
    createRoot(() => {
      initializationService = InitializationService();
    });
  });

  it('should initialize with all steps in pending state', () => {
    const state = initializationService.state();
    
    expect(state.isComplete).toBe(false);
    expect(state.hasErrors).toBe(false);
    expect(state.steps.size).toBeGreaterThan(0);
    
    // Check that all steps start as pending
    for (const [step, stepInfo] of state.steps) {
      expect(stepInfo.status).toBe(InitializationStatus.Pending);
      expect(stepInfo.retryCount).toBe(0);
    }
  });

  it('should execute steps in dependency order', async () => {
    const executionOrder: InitializationStep[] = [];
    
    // Mock executor that tracks execution order
    const createMockExecutor = (step: InitializationStep) => ({
      execute: async () => {
        executionOrder.push(step);
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    });

    // Execute a few steps
    await initializationService.executeStep(InitializationStep.UserGesture, createMockExecutor(InitializationStep.UserGesture));
    await initializationService.executeStep(InitializationStep.CoreWasm, createMockExecutor(InitializationStep.CoreWasm));
    
    expect(executionOrder).toEqual([InitializationStep.UserGesture, InitializationStep.CoreWasm]);
    expect(initializationService.isStepCompleted(InitializationStep.UserGesture)).toBe(true);
    expect(initializationService.isStepCompleted(InitializationStep.CoreWasm)).toBe(true);
  });

  it('should prevent execution of steps with unmet dependencies', async () => {
    const mockExecutor = {
      execute: vi.fn()
    };

    // Try to execute CoreWasm without UserGesture
    await expect(
      initializationService.executeStep(InitializationStep.CoreWasm, mockExecutor)
    ).rejects.toThrow('Dependencies not met');

    expect(mockExecutor.execute).not.toHaveBeenCalled();
  });

  it('should retry failed steps up to max retries', async () => {
    let attemptCount = 0;
    const mockExecutor = {
      execute: async () => {
        attemptCount++;
        if (attemptCount < 3) {
          throw new Error('Mock failure');
        }
      }
    };

    await initializationService.executeStep(InitializationStep.UserGesture, mockExecutor);
    
    expect(attemptCount).toBe(3);
    expect(initializationService.isStepCompleted(InitializationStep.UserGesture)).toBe(true);
  });

  it('should fail after max retries exceeded', async () => {
    const mockExecutor = {
      execute: async () => {
        throw new Error('Persistent failure');
      }
    };

    await expect(
      initializationService.executeStep(InitializationStep.UserGesture, mockExecutor)
    ).rejects.toThrow('Persistent failure');

    expect(initializationService.getStepStatus(InitializationStep.UserGesture)).toBe(InitializationStatus.Failed);
  });

  it('should validate steps when validator is provided', async () => {
    const mockExecutor = {
      execute: async () => {
        // Execution succeeds
      },
      validate: async () => false // But validation fails
    };

    await expect(
      initializationService.executeStep(InitializationStep.UserGesture, mockExecutor)
    ).rejects.toThrow('Validation failed');
  });

  it('should handle timeouts correctly', async () => {
    const mockExecutor = {
      execute: async () => {
        // Simulate a long-running operation
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    };

    // Set a very short timeout
    const state = initializationService.state();
    const stepInfo = state.steps.get(InitializationStep.UserGesture);
    if (stepInfo) {
      stepInfo.timeout = 50; // 50ms timeout
    }

    await expect(
      initializationService.executeStep(InitializationStep.UserGesture, mockExecutor)
    ).rejects.toThrow('Timeout');
  });

  it('should calculate progress correctly', async () => {
    const mockExecutor = {
      execute: async () => {}
    };

    const initialProgress = initializationService.calculateProgress();
    expect(initialProgress).toBe(0);

    await initializationService.executeStep(InitializationStep.UserGesture, mockExecutor);
    
    const progressAfterOne = initializationService.calculateProgress();
    expect(progressAfterOne).toBeGreaterThan(0);
    expect(progressAfterOne).toBeLessThan(100);
  });

  it('should reset state correctly', () => {
    // Execute a step first
    const mockExecutor = { execute: async () => {} };
    initializationService.executeStep(InitializationStep.UserGesture, mockExecutor);

    // Reset
    initializationService.reset();

    const state = initializationService.state();
    expect(state.isComplete).toBe(false);
    expect(state.hasErrors).toBe(false);
    
    for (const [step, stepInfo] of state.steps) {
      expect(stepInfo.status).toBe(InitializationStatus.Pending);
      expect(stepInfo.retryCount).toBe(0);
    }
  });

  it('should identify next ready step correctly', async () => {
    const mockExecutor = { execute: async () => {} };

    // Initially, UserGesture should be ready (no dependencies)
    let nextStep = initializationService.getNextReadyStep();
    expect(nextStep).toBe(InitializationStep.UserGesture);

    // After completing UserGesture, CoreWasm should be ready
    await initializationService.executeStep(InitializationStep.UserGesture, mockExecutor);
    nextStep = initializationService.getNextReadyStep();
    expect(nextStep).toBe(InitializationStep.CoreWasm);
  });
});
