"use server";

import { handleResponseCookies } from "~/lib";
import { getSession, UserSessionHelper } from "~/lib/server";
import { ImageUploadResponse, WebpageMetaDataOutput } from "~/types/api.types";
import { RoomProfileBasicInfoDto } from "~/types/room.types";
import { UserDtoUtil } from "~/types/user-helper";
import { ApiUserDto } from "~/types/user.types";
import { COMMON } from "~/util/const.common";
import NodeFetchCache, { MemoryCache } from 'node-fetch-cache';
import { SheetMusicRequest, SheetMusicUploadResponse } from "~/models/sheet-music-dbo.models";

const fetch = NodeFetchCache.create({
  shouldCacheResponse: (response) => {
    // let url = response.url.toLowerCase();
    // return response.ok && (url.includes("image") || url.includes("images"));
    return response.ok;
  },
  cache: new MemoryCache({ ttl: 60_000 * 5 })
});

/**
 * Retrieves the number of players currently online.
 *
 * @returns {Promise<number>} The number of players online.
 * @throws {Error} If an error occurs during the retrieval process.
 */
export async function getPlayersOnline(): Promise<number> {
  "use server";
  try {
    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/players-online`);
    let result = await response.json() as { count: number; };
    return result.count;
  } catch (e) {
    console.error("[getPlayersOnline] Error:", e);
    return -1;
  }
}

/**
 * Retrieves the base64 representation of an image asset.
 *
 * @param path - The path of the image asset.
 * @param isFullPath - Optional. Specifies whether the provided path is a full path or a relative path. Default is false.
 * @returns The base64 representation of the image asset, or undefined if an error occurs.
 */
export async function getAssetImage(path: string, isFullPath = false) {
  "use server";
  if (!path) return undefined;

  try {
    if (!isFullPath && (path.startsWith("http://") || path.startsWith("https://"))) isFullPath = true;
    let response = await fetch(isFullPath ? path : `${COMMON.ASSETS_URL}${path}`);
    if (!response.ok) return undefined;

    let contentType = response.headers.get("content-type") || "image/png";
    let buffer = await response.arrayBuffer();
    let base64 = Buffer.from(buffer).toString("base64");

    return `data:${contentType};base64,${base64}`;
  } catch (ex) {
    if (COMMON.IS_DEV_MODE) console.error("[getAssetImageBase64] Error:", ex);
    return undefined;
  }
}

export async function getAssetImageBlob(path: string, isFullPath = false) {
  if (!path) return undefined;

  try {
    let response = await fetch(isFullPath ? path : `${COMMON.ASSETS_URL}${path}`);
    if (!response.ok) return undefined;

    let contentType = response.headers.get("content-type") || "image/png";
    let buffer = await response.arrayBuffer();

    return { buffer, contentType };
  } catch (ex) {
    if (COMMON.IS_DEV_MODE) console.error("[getAssetImage] Error:", ex);
    return undefined;
  }
}

/**
 * Retrieves the user's profile image as a base64-encoded string.
 *
 * @param usertag - The user's tag.
 * @returns A Promise that resolves to the base64-encoded image string, or undefined if an error occurs.
 */
export async function getUserProfileImage(usertag: string, lastModified?: string | Date) {
  try {
    let session = await getSession();
    UserSessionHelper.validateTokens(session.data);

    let profileImagePath = UserDtoUtil.getUserProfileImage(usertag, lastModified);
    let response = await fetch(profileImagePath, {
      headers: UserSessionHelper.getHeaders(session.data)
    });

    if (!response.ok) return undefined;
    let contentType = response.headers.get("content-type") || "image/png";

    let buffer = await response.arrayBuffer();
    let base64 = Buffer.from(buffer).toString("base64");
    return `data:${contentType};base64,${base64}`;
  } catch (e) {
    console.error("[getUserProfileImage] Error:", e);
    return undefined;
  }
}

/**
 * Retrieves the websocket identity.
 *
 * @returns A Promise that resolves to the websocket identity, or undefined if an error occurs.
 */
export async function getWebsocketIdentity() {
  "use server";
  let session = await getSession();

  try {
    UserSessionHelper.validateTokens(session.data);
  } catch {
    throw new Error("Invalid session.");
  }

  let response = await fetch(
    `${process.env.PIANORHYTHM_SERVER_URL}/api/session-state?cb=${Date.now()}`,
    {
      method: "GET",
      headers: UserSessionHelper.getHeaders(session.data)
    });

  if (!response.ok) {
    // try { console.log(await response.text()); } catch { }
    console.error("[getWebsocketIdentity] Error:", response.statusText);
    throw new Error(response.statusText);
  }

  await handleResponseCookies(response as any).catch(() => {
  });

  return { identity: await response.text() };
}

/**
 * Retrieves the content of an asset file as text.
 *
 * @param path - The path of the asset file.
 * @param isFullPath - Optional. Specifies whether the provided path is a full path or a relative path. Default is false.
 * @returns A promise that resolves to the text content of the asset file, or undefined if the file cannot be retrieved.
 */
export async function getAssetAsText(path: string, isFullPath = false) {
  if (!path) return undefined;

  try {
    let response = await fetch(isFullPath ? path : `${COMMON.ASSETS_URL}${path}`);
    if (!response.ok) return undefined;

    return await response.text();
  } catch (ex) {
    if (COMMON.IS_DEV_MODE) console.error("[getAssetAsText] Error:", ex);
    return undefined;
  }
}

/**
 * Uploads an image to the specified path.
 *
 * @param path - The path where the image will be uploaded.
 * @param data - The image data to be uploaded.
 * @returns A promise that resolves to the response of the image upload.
 * @throws An error if the path or data is invalid, or if the upload fails.
 */
export async function uploadImage(path: string, data: string) {
  "use server";
  try {
    if (!path) throw new Error("Invalid path.");
    if (!data) throw new Error("Invalid data.");

    const session = await getSession();
    UserSessionHelper.validateTokens(session.data);

    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}${path}`, {
      method: "POST",

      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data)
      },
      body: JSON.stringify({ data })
    });

    let getJson = async () => await response.json() as ImageUploadResponse;

    if (!response.ok) {
      try {
        return await getJson();
      } catch {
      }
      throw new Error(response.statusText);
    }

    return await getJson();
  } catch (ex) {
    if (COMMON.IS_DEV_MODE) console.error("[uploadImage] Error:", ex);
    return ex as Error;
  }
}

/**
 * Deletes a member account.
 *
 * @param password - The password of the account. Required if `isOAuth` is `false`.
 * @param isOAuth - Indicates if the account is an OAuth account. Defaults to `false`.
 * @returns A boolean indicating if the deletion was successful.
 * @throws {Error} If the password is invalid and `isOAuth` is `false`.
 */
export async function deleteMemberAccount(
  password?: string,
  isOAuth: boolean = false
) {
  "use server";
  if (!password && !isOAuth) throw new Error("Invalid password.");

  const session = await getSession();
  UserSessionHelper.validateTokens(session.data);

  //TODO: Api for deletion doesn't exist yet
  let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/users/members/delete-account`, {
    method: "POST",

    headers: UserSessionHelper.getHeaders(session.data),
    body: JSON.stringify({ password, isOAuth })
  });

  return response.ok;
}

export async function getApiServerHost() {
  "use server";
  return process.env.PIANORHYTHM_SERVER_URL;
}

export async function getRoomSettings(roomID: string): Promise<RoomProfileBasicInfoDto | undefined> {
  "use server";
  try {
    let session = await getSession();
    UserSessionHelper.validateTokens(session.data);

    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/rooms/basic-profile-info/${roomID}`, {
      headers: UserSessionHelper.getHeaders(session.data)
    });

    return await response.json() as RoomProfileBasicInfoDto;
  } catch (e) {
    console.error("[getRoomSettings] Error:", e);
    return undefined;
  }
}

export async function getActiveUser(targetSocketID: string): Promise<ApiUserDto | undefined> {
  "use server";
  try {
    let session = await getSession();
    UserSessionHelper.validateTokens(session.data);

    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/users/active/user/${targetSocketID}`, {

      headers: UserSessionHelper.getHeaders(session.data)
    });

    let json = await response.json() as any;
    if (json.error) {
      return undefined;
    }

    return json as ApiUserDto;
  } catch (e) {
    console.error("[getActiveUser] Error:", e);
    return undefined;
  }
}

export async function getUserBasicAccount(targetUsertag: string): Promise<ApiUserDto | undefined> {
  "use server";
  try {
    let session = await getSession();
    UserSessionHelper.validateTokens(session.data);

    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/users/user-account/${btoa(targetUsertag)}`, {
      headers: UserSessionHelper.getHeaders(session.data)
    });

    let json = await response.json() as any;
    if (json?.error) {
      return undefined;
    }

    return json as ApiUserDto;
  } catch (e) {
    console.error("[getUserBasicAccount] Error:", e);
    return undefined;
  }
}

export async function getDesktopAppURL(platform: string | undefined, is32Bit = false) {
  "use server";
  if (!platform) return null;

  try {
    let session = await getSession();
    UserSessionHelper.validateTokens(session.data);

    let result = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/get-desktop-client?target=${platform.toLowerCase()}&arch=${is32Bit ? "x86" : "x64"}`, {
      headers: UserSessionHelper.getHeaders(session.data)
    });

    let data = await result.json() as { url: string, version: string; };
    console.log(data);

    return { url: data.url, version: data.version || "0.0.0" };
  } catch (e) {
    console.error("[getDesktopAppURL] Error:", e);
    return null;
  }
}

export async function getCountryFlagImage(countryCode: string) {
  return getAssetImage(`https://ipgeolocation.io/static/flags/${countryCode.toLowerCase()}_64.png`, true);
}

export async function getCrownImage() {
  return getAssetImage("/images/crown.png");
}

export async function scrapeUrl(url: string): Promise<WebpageMetaDataOutput | undefined> {
  "use server";
  try {
    let session = await getSession();
    UserSessionHelper.validateTokens(session.data);

    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/scrape-url`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data)
      },
      body: JSON.stringify({ url })
    });

    return await response.json() as WebpageMetaDataOutput;
  } catch (e) {
    console.error("[scrapeUrl] Error:", e);
    return undefined;
  }
}

export const sheetMusicUpsert = async (request: SheetMusicRequest, path: string, method: string = "POST") => {
  "use server";

  try {
    let session = await getSession();
    UserSessionHelper.validateTokens(session.data);

    let bodyFormData = new FormData();

    if (request.data) {
      bodyFormData.append("file", new Blob([request.data], { type: "text/plain" }));
    }
    bodyFormData.append("request", JSON.stringify(request));

    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/server/sheet_music/${path}`, {
      method: method.toUpperCase(),
      headers: {
        ...UserSessionHelper.getHeaders(session.data)
      },
      body: bodyFormData
    });

    return await response.json() as SheetMusicUploadResponse;
  } catch (e) {
    console.error("[sheetMusicUpsertBase] Error:", e);
    return { error: e instanceof Error ? e.message : String(e) };
  }
};