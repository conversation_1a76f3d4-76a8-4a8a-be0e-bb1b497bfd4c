import { vi } from "vitest";
import AppService from "~/services/app.service";
import DatabaseServiceController from "~/services/db.service";
import AppSettingsService from "~/services/settings-storage.service";
import SoundEffectsService from "~/services/sound-effects.service";
import { CoreService } from "~/types/app.types";
import { MockEventBus } from "./common.mocks";
import WebsocketService from "~/services/websocket.service";
import EmojifyService from "~/services/emojify.service";
import I18nService from "~/services/i18n.service";
import { Subject } from "rxjs";
import DisplaysService, { DEFAULT_DISPLAYS } from "~/services/displays.service";
import ChatService from "~/services/chat.service";
import UsersService from "~/services/users-service";
import { ClientSideUserDto } from "~/proto/user-renditions";
import { SocketID } from "~/types/user.types";
import KeyboardService from "~/services/keyboard.service";
import WebMidiService from "~/services/webmidi.service";
import NotificationService from "~/services/notification.service";
import PlatformService from "~/services/platform.service";
import InitializationService from "~/services/initialization.service";
import {
  InitializationStep,
  InitializationStatus,
  InitializationState,
  InitializationConfig
} from "~/types/initialization.types";

export const MockCoreService = () => ({
  create_synth: vi.fn(),
  create_synth_stream: vi.fn(),
  reset_app_state: vi.fn(),
  midi_io_start: vi.fn(),
  init_note_buffer_engine: vi.fn(),
  flush_note_buffer_engine: vi.fn(),
  init_wasm: vi.fn(),
  get_core_version: vi.fn(),
  get_synth_version: vi.fn(),
  get_renderer_version: vi.fn(),
  get_hash_socket_id: vi.fn(),
  websocket_connect: vi.fn(),
  websocket_send_binary: vi.fn(),
  websocket_disconnect: vi.fn(),
  emit_core_app_actions: vi.fn(),
  synth_load_soundfont: vi.fn(),
  send_app_action: vi.fn(),
  send_app_action_bytes: vi.fn(),
  send_app_synth_action: vi.fn(),
  synth_sustain: vi.fn(),
  get_wasm_module: vi.fn(),
  get_wasm_memory: vi.fn(),
  set_slot_mode: vi.fn(),
  hash_device_id: vi.fn(),
  all_sounds_off: vi.fn(),
  all_notes_off: vi.fn(),
  reset_all_controllers: vi.fn(),
  set_channel_active: vi.fn(),
  volume_change: vi.fn(),
  pan_change: vi.fn(),
  damper_pedal: vi.fn(),
  bank_select: vi.fn(),
  program_select: vi.fn(),
  from_socket_note_on: vi.fn(),
  clear_program_on_channel: vi.fn(),
  note_on: vi.fn(),
  note_off: vi.fn(),
  from_socket_note_off: vi.fn(),
  load_soundfont: vi.fn(),
  set_max_multi_mode_channels: vi.fn(),
  synth_set_gain: vi.fn(),
  synth_set_user_gain: vi.fn(),
  synth_set_polyphony: vi.fn(),
  synth_set_sample_rate: vi.fn(),
  synth_set_chorus: vi.fn(),
  synth_set_reverb: vi.fn(),
  parse_midi_data: vi.fn(),
  parse_midi_data_non_proxy: vi.fn(),
  instrument_exists: vi.fn(),
  get_program: vi.fn(),
  set_client_socket_id: vi.fn(),
  mute_user: vi.fn(),
  add_socket: vi.fn(),
  remove_socket: vi.fn(),
  set_octave_offset: vi.fn(),
  set_transpose_offset: vi.fn(),
  set_interpolation_method: vi.fn(),
  get_synth_users: vi.fn(),
  synth_get_reverb_level: vi.fn(),
  synth_get_reverb_room_size: vi.fn(),
  synth_get_reverb_width: vi.fn(),
  synth_get_reverb_damp: vi.fn(),
  synth_set_reverb_width: vi.fn(),
  synth_set_reverb_level: vi.fn(),
  set_primary_channel: vi.fn(),
  synth_set_reverb_room_size: vi.fn(),
  synth_set_reverb_damp: vi.fn(),
  set_disable_velocity_for_client: vi.fn(),
  dispose: vi.fn(),
  disconnect: vi.fn(),
  reset: vi.fn(),
  open_midi_input_connection: vi.fn(),
  open_midi_output_connection: vi.fn(),
  emit_to_midi_output: vi.fn(),
  close_midi_input_connection: vi.fn(),
  close_midi_output_connection: vi.fn(),
  list_midi_input_connections: vi.fn(),
  list_midi_output_connections: vi.fn(),
  load_midi_file: vi.fn(),
  load_midi_file_by_path: vi.fn(),
  stop_midi_file: vi.fn(),
  resume_midi_file: vi.fn(),
  pause_midi_file: vi.fn(),
  synth_set_auto_fill_channels_with_default: vi.fn(),
  set_use_default_instrument_when_missing_for_other_users: vi.fn(),
  bypassall_equalizer: vi.fn(),
  set_drum_channel_muted: vi.fn(),
  set_equalizer_enabled: vi.fn(),
  set_midi_output_only: vi.fn(),
  set_max_velocity: vi.fn(),
  set_min_velocity: vi.fn(),
  set_max_note_on_time: vi.fn(),
  set_channel_volume: vi.fn(),
  reset_equalizer: vi.fn(),
  get_all_presets_from_sf: vi.fn(),
  get_synth_audio_channels: vi.fn(),
  set_equalizer_resonance: vi.fn(),
  set_equalizer_bypass: vi.fn(),
  set_equalizer_freq: vi.fn(),
  set_equalizer_gain: vi.fn(),
  set_equalizer_band: vi.fn(),
  set_user_volume: vi.fn(),
  set_user_velocity_percentage: vi.fn(),
  note_buffer_engine_set_self_hosted: vi.fn(),
  core_to_renderer_effects: vi.fn(),
  core_to_renderer_events: vi.fn(),
  webrtc_connect: vi.fn(),
  webrtc_disconnect: vi.fn(),
  webrtc_send: vi.fn(),
  webrtc_receive: vi.fn(),
  webrtc_get_stats: vi.fn(),
  from_socket_pitch: vi.fn(),
  synth_ws_socket_note_on: vi.fn(),
  synth_ws_socket_pitch: vi.fn(),
  set_apply_velocity_curve: vi.fn()
}) as CoreService;

/**
 * MockSoundEffectsService is a mock implementation of the SoundEffectsService interface.
 * It provides dummy implementations for various sound effect methods.
 */
export const MockSoundEffectsService = () => ({
  playClickSFX: vi.fn(),
  initialize: vi.fn(),
  playHoverSFX: vi.fn(),
  playChatMessageSendSFX: vi.fn(),
  playChatMessageInSFX: vi.fn(),
  playSFX: vi.fn(),
  stageEffectsVolume: vi.fn(),
  playSFX_ui2: vi.fn(),
  playModalOpenSFX: vi.fn(),
  playModalCloseSFX: vi.fn(),
  playNotificationSuccessSFX: vi.fn(),
  playErrorSFX: vi.fn()
}) as ReturnType<typeof SoundEffectsService>;

/**
 * MockAppService is a mock implementation of the AppService interface.
 * It provides dummy implementations for various methods and properties.
 * This is useful for testing or mocking the behavior of the AppService in a controlled environment.
 */
export const MockAppService = () => ({
  loadCoreWasm: vi.fn(),
  canvasWorker: vi.fn(),
  isRoomCurrentPage: vi.fn(),
  userGestureController: new AbortController(),
  canInteract: vi.fn(),
  currentPage: vi.fn(),
  setCurrentPage: vi.fn(),
  initialized: vi.fn(),
  setInitialized: vi.fn(),
  coreService: MockCoreService,
  firstTimeJoinRoom: vi.fn(),
  setFirstTimeJoinRoom: vi.fn(),
  currentRoomParam: vi.fn(),
  setCurrentRoomParam: vi.fn(),
  onDisconnect: vi.fn() as any,
  offlineMode: vi.fn(),
  welcomeEvent: vi.fn(),
  isClientInDoNotDisturb: vi.fn(),
  appStateEffects: MockEventBus(),
  appStateEvents: MockEventBus(),
  onDisconnectEvents: MockEventBus(),
  setCoreService: vi.fn(),
  serverServiceDown: vi.fn(),
  setServerServiceDown: vi.fn(),
  onHandleAppEffects: vi.fn(),
  onHandleAppEvent: vi.fn(),
  sendInitializeAppState: vi.fn(),
  decodeAppEffect: vi.fn(),
  encodeAppEffect: vi.fn(),
  queuedEffectsForRendererConsumed: vi.fn(),
  setQueuedEffectsForRendererConsumed: vi.fn(),
  queuedEffectsForRenderer: vi.fn(),
  setQueuedEffectsForRenderer: vi.fn(),
  activatePageLoader: vi.fn(),
  setActivatePageLoader: vi.fn(),
  queuedEventsForRenderer: vi.fn(),
  setQueuedEventsForRenderer: vi.fn(),
  pagerLoaderAnimating: vi.fn(),
  setPagerLoaderAnimating: vi.fn(),
  activePageLoaderToolTip: vi.fn(),
  setActivePageLoaderToolTip: vi.fn(),
  setRoomOwner: vi.fn(),
  setClientIsSelfNotesMuted: vi.fn(),
  isUserRoomOwner: vi.fn(),
  doesClientHaveSheetMusicFullAccess: vi.fn(),
  doesClientHaveMidiMusicFullAccess: vi.fn(),
  clientIsSelfNotesMuted: vi.fn(),
  setClientIsSelfChatMuted: vi.fn(),
  canReport: vi.fn(),
  setUserNotificationSettings: vi.fn(),
  userNotificationSettings: vi.fn(),
  clientIsSelfChatMuted: vi.fn(),
  isClientServerNotesMuted: vi.fn(),
  appVersionMismatched: vi.fn(),
  roomType: vi.fn(),
  // themeColors: {
  //   primary: 'your_primary_color_value',
  //   accent: 'your_accent_color_value',
  //   tertiary: 'your_tertiary_color_value',
  // },
  roomSettings: vi.fn(),
  roomName: vi.fn(),
  setAppVersionMismatched: vi.fn(),
  maintenanceModeActive: vi.fn(),
  setMaintenanceModeActive: vi.fn(),
  canvasLoaded: vi.fn(),
  _setCanvasLoaded: vi.fn(),
  // setTheme: vi.fn(),
  roomID: vi.fn(),
  coreWasmLoaded: vi.fn(),
  isClientMember: vi.fn(),
  isClientMod: vi.fn(),
  isClientRoomOwner: vi.fn(),
  isClientAdmin: vi.fn(),
  isClientProMember: vi.fn(),
  getClientStatus: vi.fn(),
  getUserTag: vi.fn(),
  getClientMetaDetails: vi.fn(),
  getSocketID: vi.fn(),
  client: vi.fn(),
  clientHasEveryoneElseMuted: vi.fn(),
  setClientHasEveryoneElseMuted: vi.fn(),
  sendIsMobileAppAction: vi.fn(),
  isClient: vi.fn(),
  roomOwner: vi.fn(),
  clearClient: vi.fn(),
  wasmMidiSequencerEffects: MockEventBus(),
  createAppAction: vi.fn(),
  updateRoomDetails: vi.fn(),
  clientLoaded: vi.fn(),
  setRoomSettings: vi.fn(),
  isClientInRoom: vi.fn(),
  is2DMode: vi.fn(),
  sceneMode: vi.fn(),
  setSceneMode: vi.fn(),
  desktopSynthEvents: new Subject(),
  webMidiConnectionEvents: MockEventBus(),
  onClientLoaded: vi.fn()
}) as ReturnType<typeof AppService>;

/**
 * MockAppSettingsService is a mock implementation of the AppSettingsService interface.
 * It provides dummy implementations for various methods and properties.
 * This mock is typically used for testing purposes.
 */
export const MockAppSettingsService = () => ({
  setLocalStorage: vi.fn(),
  getSettings: vi.fn(),
  persistSettings: vi.fn() as any,
  getSetting: vi.fn(),
  saveSetting: vi.fn(),
  getLocalStorage: vi.fn(),
  loadSettingsFromServer: vi.fn(),
  loadGuestSettings: vi.fn(),
  clearMemberSettings: vi.fn(),
  clearGuestSettings: vi.fn(),
  clearSettings: vi.fn(),
  resetSettingsToDefault: vi.fn(),
  resetGraphicsSettingsToDefault: vi.fn(),
  resetSoundFxSettingsToDefault: vi.fn(),
  resetAudioSoundfontSettingsToDefault: vi.fn(),
  isDebugMode: vi.fn(),
  initialize: vi.fn(),
  onDisconnect: vi.fn(),
  isClientGuest: vi.fn(),
  setIsClientGuest: vi.fn(),
  settingSaved: vi.fn(),
  persistSettingsEvent: MockEventBus(),
  settingsUpdatedEvents: MockEventBus()
}) as ReturnType<typeof AppSettingsService>;

/**
 * MockDbService is a mock implementation of the DatabaseServiceController interface.
 * It provides mock implementations for various methods such as initialize, hasKey, dispose, etc.
 * This mock service is typically used for testing purposes to simulate the behavior of a real database service.
 */
export const MockDbService = () => ({
  initialize: vi.fn(),
  hasKey: vi.fn(),
  dispose: vi.fn(),
  clear: vi.fn(),
  deleteKey: vi.fn(),
  put: vi.fn(),
  get: vi.fn(),
  getAllKeys: vi.fn(),
  getAllValues: vi.fn(),
  getAllEntries: vi.fn(),
  createStore: vi.fn(),
  onDisconnect: vi.fn()
}) as ReturnType<typeof DatabaseServiceController>;

export const MockWebSocketService = () => ({
  initialize: vi.fn(),
  triggerClientSelfMute: vi.fn(),
  emitRoomChatServerCommand: vi.fn(),
  emitUserUpdateCommand: vi.fn(),
  joinRoomByName: vi.fn(),
  postEmitMessage: vi.fn(),
  joinNextAvailableLobby: vi.fn(),
  emitProtoServerMessageOfCommand: vi.fn(),
  joinRoomByID: vi.fn(),
  connect: vi.fn(),
  emitChatMessage: vi.fn(),
  connected: vi.fn(),
  disconnect: vi.fn(),
  createOrUpdateRoom: vi.fn(),
  emitIsTyping: vi.fn(),
  emitServerCommand: vi.fn(),
  emitServerModCommand: vi.fn(),
  websocketEvents: MockEventBus(),
  onDisconnect: vi.fn(),
  logout: vi.fn()
}) as ReturnType<typeof WebsocketService>;

export const MockEmojifyService = () => ({
  encode: vi.fn(),
  decode: vi.fn(),
  decodeRaw: vi.fn()
}) as ReturnType<typeof EmojifyService>;

export const MockI18nService = () => ({
  initialize: vi.fn(),
  dispose: vi.fn(),
  setActiveLanguage: vi.fn(),
  activeLanguage: vi.fn(),
  t_roomPageActionWidgets: vi.fn(),
  t_roomPageSettings: vi.fn(),
  t_roomPage: vi.fn(),
  t_server: vi.fn(),
  t_common: vi.fn(),
  t_loginPage: vi.fn(),
  t_roomPageSettingSubMenuLabel: vi.fn(),
  t_roomPageSettingHeader: vi.fn(),
  t_roomPageSettingTooltip: vi.fn(),
  t_roomPageSettingSubMenuElement: vi.fn(),
  t_roomPageBottomBarButtons: vi.fn(),
  t_roomPageBottomBarTooltips: vi.fn(),
  t_roomPageBottomBarMessages: vi.fn(),
  t_roomPageInstDockButtons: vi.fn(),
  t_roomPageInstDockToolNames: vi.fn(),
  t_roomPageInstDockToolNamesToolTips: vi.fn()
}) as ReturnType<typeof I18nService>;

export const MockDisplaysService = () => ({
  initialize: vi.fn(),
  initialized: vi.fn(),
  docsURLParameters: vi.fn(),
  setDocsURLParameters: vi.fn(),
  sidebarDocsPath: vi.fn(),
  clearSidebarDocsPath: vi.fn(),
  setSidebarDocsPath: vi.fn(),
  docsModalTitle: vi.fn(),
  setDocsModalTitle: vi.fn(),
  autoHideBottomBar: vi.fn(),
  setAutoHideBottomBar: vi.fn(),
  displays: { ...DEFAULT_DISPLAYS },
  lastModalDisplay: vi.fn(),
  modalsOpened: vi.fn(),
  onDisconnect: vi.fn(),
  getDisplay: vi.fn(),
  hideAll: vi.fn(),
  getLastModalOpened: vi.fn(),
  getLastModalsOpened: vi.fn(),
  setDisplay: vi.fn(),
  setDisplayMainUI: vi.fn(),
  anyModalsOpened: vi.fn(),
  setAnyModalsOpened: vi.fn(),
  toggleDisplay: vi.fn()
}) as ReturnType<typeof DisplaysService>;

export const MockDatabaseServiceController = () => ({
  initialize: vi.fn(),
  hasKey: vi.fn(),
  dispose: vi.fn(),
  clear: vi.fn(),
  deleteKey: vi.fn(),
  put: vi.fn(),
  get: vi.fn(),
  getAllKeys: vi.fn(),
  getAllValues: vi.fn(),
  getAllEntries: vi.fn(),
  createStore: vi.fn(() => ({
    put: vi.fn(),
    deleteKey: vi.fn(),
    getAllKeys: vi.fn()
  })),
  onDisconnect: vi.fn()
});

/**
 * MockChatService is a mock implementation of the ChatService.
 * It provides mock implementations for various methods and properties used in chat functionality.
 */
export const MockChatService = () => ({
  initialize: vi.fn(),
  loadServerModCommands: vi.fn(),
  loadRoomOwnerCommands: vi.fn(),
  unloadRoomOwnerCommands: vi.fn(),
  addMessage: vi.fn(),
  messages: vi.fn().mockReturnValue([]),
  chatBarElement: vi.fn(),
  checkIfCommandCanBeRan: vi.fn(),
  findMessagesBy: vi.fn(),
  runRoomOwnerChatCommand: vi.fn(),
  runServerModChatCommand: vi.fn(),
  runModChatCommand: vi.fn(),
  runClientChatCommand: vi.fn(),
  isSystemMessage: vi.fn(),
  canDeleteMessage: vi.fn(),
  canEditMessage: vi.fn(),
  canReplyToMessage: vi.fn(),
  onEditMessage: vi.fn(),
  addedMessagesEvents: new Subject(),
  editedMessagesEvents: new Subject(),
  deletedMessagesEvents: new Subject(),
  DEFAULT_CHAT_LIST_WIDTH: 300,
  runRoomChatServerCommand: vi.fn(),
  chatCommands: vi.fn().mockReturnValue([]),
  chatCommandModules: vi.fn().mockReturnValue([]),
  setChatBarElement: vi.fn(),
  setChatBarInputElement: vi.fn(),
  activeChatCtxID: vi.fn(),
  chatMessageToHighlight: vi.fn(),
  anyListCommandsDisplayed: vi.fn().mockReturnValue(false),
  setAnyListCommandsDisplayed: vi.fn(),
  triggerClearProfileDescription: vi.fn(),
  clearNickname: vi.fn(),
  clearProfileDescription: vi.fn(),
  clearStatusText: vi.fn(),
  chatBarValue: vi.fn(),
  setChatBarValue: vi.fn(),
  setChatCommandsDisplayed: vi.fn(),
  chatMessageBeingRepliedTo: vi.fn(),
  chatMessageBeingEdited: vi.fn(),
  chatMessagesSetToBeDeleted: vi.fn().mockReturnValue([]),
  messsageHasTaggedUser: vi.fn(),
  getIndexByMessageID: vi.fn(),
  getMessageByIndex: vi.fn(),
  canReportMessage: vi.fn(),
  getMessageByID: vi.fn(),
  setChatMessagesSetToBeDeleted: vi.fn(),
  setChatMessageToHighlight: vi.fn(),
  setChatMessageBeingRepliedTo: vi.fn(),
  setChatMessageBeingEdited: vi.fn(),
  setActiveMessageItemIndexToShowOptionsMenu: vi.fn(),
  activeMessageItemIndexToShowOptionsMenu: vi.fn().mockReturnValue(null),
  setChatMinimized: vi.fn(),
  setChatOpacity: vi.fn(),
  setShowChatBar: vi.fn(),
  alwaysAutoScroll: vi.fn().mockReturnValue(false),
  setShowChatWindowButtons: vi.fn(),
  setChatMaximized: vi.fn(),
  chatMaximized: vi.fn().mockReturnValue(false),
  chatBarInputElement: vi.fn(),
  showChatBar: vi.fn().mockReturnValue(true),
  disposeContextMenu: vi.fn(),
  setChatContainerElement: vi.fn(),
  showChatWindowButtons: vi.fn().mockReturnValue(true),
  chatMinimized: vi.fn().mockReturnValue(false),
  chatOpacity: vi.fn().mockReturnValue(1),
  ctxMenuActive: vi.fn().mockReturnValue(false),
  chatCommandsDisplayed: vi.fn().mockReturnValue(false),
  preventDefaultIfChatCommandsDisplayed: vi.fn(),
  defaultChatOpacity: 1,
  ContainerWidth: 300,
  usersTyping: vi.fn().mockReturnValue([]),
  setAlwaysAutoScroll: vi.fn(),
  recentMentionedUsers: vi.fn().mockReturnValue([]),
  selectedMentionedUser: vi.fn(),
  setSelectedMentionedUser: vi.fn(),
  clearRecentMentionedUsers: vi.fn(),
  setLastSelectedUser: vi.fn()
}) as ReturnType<typeof ChatService>;

/**
 * MockUsersService is a mock implementation of the UsersService.
 * It provides mock implementations for all methods and properties of the UsersService.
 * This is useful for testing components that depend on the UsersService.
 */
export const MockUsersService = () => {
  // Create a mock users array that can be manipulated in tests
  const mockUsers: ClientSideUserDto[] = [];

  return {
    // Core methods
    initialize: vi.fn(),
    getUserBySocketID: vi.fn((socketId?: string) =>
      mockUsers.find(user => user.socketID.toLowerCase() === socketId?.toLowerCase())
    ),
    fetchUserBasicAccount: vi.fn(),
    fetchActiveUser: vi.fn(),
    onUserConnect: vi.fn(),

    // Muted users management
    addMutedNotesUser: vi.fn(async (socketID: SocketID) => {
    }),
    removeMutedNotesUser: vi.fn(async (socketID: SocketID) => {
    }),
    addMutedChatUser: vi.fn(async (socketID: SocketID) => {
    }),
    removeMutedChatUser: vi.fn(async (socketID: SocketID) => {
    }),

    // User lookup methods
    getUserByUsertag: vi.fn((usertag?: string) =>
      mockUsers.find(user => user.userDto?.usertag.toLowerCase() === usertag?.toLowerCase())
    ),
    getUserVolumeBySocketID: vi.fn(() => 100),

    // Users data access
    users: mockUsers,
    getUsers: vi.fn(() => mockUsers.map(x => ({ ...x }) as ClientSideUserDto)),

    // Helper method to add users to the mock for testing
    _addMockUser: (user: ClientSideUserDto) => {
      mockUsers.push(user);
    },

    // Helper method to clear all mock users
    _clearMockUsers: () => {
      mockUsers.length = 0;
    }
  } as ReturnType<typeof UsersService>;
};

/**
 * MockKeyboardService is a mock implementation of the KeyboardService.
 * It provides mock implementations for keyboard-related functionality.
 */
export const MockKeyboardService = () => ({
  initialize: vi.fn(),
  keyboardLayout: vi.fn(),
  setKeyboardLayout: vi.fn(),
  getShortcutForCommand: vi.fn(),
  playAreaIsFocused: vi.fn(),
  setPlayAreaIsFocused: vi.fn(),
  canPlayKeys: vi.fn(),
  customLayoutKeybinds: vi.fn(),
  setCustomLayoutKeybinds: vi.fn(),
  getDefaultShortcut: vi.fn(),
  keybinds: vi.fn(),
  editingKeybinds: vi.fn(),
  setEditingKeybinds: vi.fn(),
  setKeybinds: vi.fn(),
  defaultKeybinds: vi.fn(),
  onDisconnect: vi.fn(),
  altKeyPressed: vi.fn(),
  shiftKeyPressed: vi.fn(),
  getAllMappings: vi.fn(),
  isInCustomizeLayoutKeysMode: vi.fn(),
  setIsInCustomizeLayoutKeysMode: vi.fn()
}) as ReturnType<typeof KeyboardService>;

/**
 * MockWebMidiService is a mock implementation of the WebMidiService.
 * It provides mock implementations for MIDI input/output functionality.
 */
export const MockWebMidiService = () => ({
  initialize: vi.fn(),
  dispose: vi.fn(),
  midiInputs: vi.fn(),
  midiOutputs: vi.fn(),
  emitToMidiOutput: vi.fn(),
  refreshMidiInputsState: vi.fn(),
  refreshMidiOutputsState: vi.fn(),
  setMultipleInputsActive: vi.fn(),
  setMultipleOutputsActive: vi.fn(),
  getActiveMidiInputIDs: vi.fn(),
  getActiveMidiOutputIDs: vi.fn(),
  getinactiveMidiInputIDs: vi.fn(),
  getinactiveMidiOutputIDs: vi.fn(),
  hasMidiPermission: vi.fn(),
  initialized: vi.fn(),
  refreshMidiState: vi.fn(),
  setInputActive: vi.fn(),
  setOutputActive: vi.fn(),
  toggleInput: vi.fn(),
  toggleOutput: vi.fn()
}) as ReturnType<typeof WebMidiService>;

/**
 * MockNotificationService is a mock implementation of the NotificationService.
 * It provides mock implementations for notification functionality.
 */
export const MockNotificationService = {
  show: vi.fn(),
  hide: vi.fn()
};

/**
 * MockPlatformService is a mock implementation of the PlatformService.
 * It provides mock implementations for platform detection functionality.
 */
export const MockPlatformService = () => ({
  initialized: vi.fn().mockReturnValue(true),
  initialize: vi.fn(),
  isMobile: vi.fn().mockReturnValue(false),
  isFireFox: vi.fn().mockReturnValue(false),
  osPlatform: vi.fn().mockReturnValue("Windows"),
  is64Bit: vi.fn().mockReturnValue(true)
}) as ReturnType<typeof PlatformService>;

/**
 * MockInitializationService is a mock implementation of the InitializationService.
 * It provides mock implementations for initialization step management functionality.
 */
export const MockInitializationService = () => {
  const mockState: InitializationState = {
    steps: new Map(),
    isComplete: false,
    hasErrors: false,
    startTime: Date.now()
  };

  const mockConfig: InitializationConfig = {
    defaultTimeout: 30000,
    maxRetries: 3,
    retryDelay: 1000,
    enableLogging: false
  };

  return {
    state: vi.fn().mockReturnValue(mockState),
    executeStep: vi.fn().mockImplementation(async (step: InitializationStep, executor: any) => {
      // Mock successful execution of any step
      try {
        if (executor.execute) {
          await executor.execute();
        }
        if (executor.validate) {
          return await executor.validate();
        }
        return true;
      } catch (error) {
        // For testing purposes, we'll catch and ignore errors to prevent test failures
        // In real scenarios, the InitializationService would handle retries and error reporting
        // console.warn(`Mock InitializationService: Step ${step} failed:`, error);
        return false;
      }
    }),
    waitForStep: vi.fn().mockResolvedValue(undefined),
    getStepStatus: vi.fn().mockReturnValue(InitializationStatus.Completed),
    isStepCompleted: vi.fn().mockReturnValue(true),
    reset: vi.fn(),
    getNextReadyStep: vi.fn().mockReturnValue(null),
    setProgressCallback: vi.fn(),
    setErrorCallback: vi.fn(),
    calculateProgress: vi.fn().mockReturnValue(100),
    config: mockConfig
  } as ReturnType<typeof InitializationService>;
};