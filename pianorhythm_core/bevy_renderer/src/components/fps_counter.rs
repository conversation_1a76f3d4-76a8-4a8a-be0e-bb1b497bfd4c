use bevy::diagnostic::{DiagnosticsStore, FrameTimeDiagnosticsPlugin};
use bevy::prelude::*;

use crate::resources::ClientAppSettings;

/// Marker to find the container entity so we can show/hide the FPS counter
#[derive(Component)]
pub struct FpsRoot;

/// Marker to find the text entity so we can update it
#[derive(Component)]
pub struct FpsText;

const TARGET_MAX_FPS: f64 = 60.0;
const TARGET_MID_FPS: f64 = 30.0;
const TARGET_MIN_FPS: f64 = 15.0;

pub fn setup_fps_counter(mut commands: Commands) {
    commands
        .spawn((
            FpsRoot,
            ZIndex(i32::MAX),
            GlobalZIndex(i32::MAX),
            Text::new("FPS:"),
            TextLayout::default(),
            TextFont {
                font_size: 12.0,
                ..default()
            },
            Node {
                position_type: PositionType::Absolute,
                right: Val::Percent(1.),
                top: Val::Percent(1.),
                bottom: Val::Auto,
                left: Val::Auto,
                padding: UiRect::all(Val::Px(4.0)),
                ..Default::default()
            },
        ))
        .with_child((
            FpsText,
            TextSpan::new("N/A"),
            TextColor::from(Color::WHITE),
            TextFont {
                font_size: 12.0,
                ..default()
            },
        ));
}

pub fn fps_text_update_system(
    diagnostics: Option<Res<DiagnosticsStore>>, 
    mut query: Query<(&mut TextSpan, &mut TextColor), With<FpsText>>
) {
    let diagnostics = if let Some(diagnostics) = diagnostics {
        diagnostics
    } else {
        return;
    };

    for (mut text, mut text_color) in &mut query {
        if let Some(value) = diagnostics.get(&FrameTimeDiagnosticsPlugin::FPS).and_then(|fps| fps.smoothed()) {
            **text = format!("{value:>3.0}").to_string();

            let new_color = if value >= TARGET_MAX_FPS {
                Color::srgb(0.0, 1.0, 0.0)
            } else if value >= 60.0 {
                Color::srgb((1.0 - (value - TARGET_MID_FPS) / (TARGET_MAX_FPS - TARGET_MID_FPS)) as f32, 1.0, 0.0)
            } else if value >= TARGET_MIN_FPS {
                Color::srgb(1.0, ((value - TARGET_MIN_FPS) / (TARGET_MID_FPS - TARGET_MIN_FPS)) as f32, 0.0)
            } else {
                Color::srgb(1.0, 0.0, 0.0)
            };

            *text_color = TextColor::from(new_color);
        } else {
            **text = "N/A".to_string();
            *text_color = TextColor::from(Color::WHITE);
        }
    }
}

/// Toggle the FPS counter
pub fn fps_counter_showhide(mut q: Query<&mut Visibility, With<FpsRoot>>, app_settings: Res<ClientAppSettings>) {
    let Ok(mut vis) = q.single_mut() else {
        return;
    };

    *vis = if app_settings.0.DISPLAY_FPS {
        Visibility::Visible
    } else {
        Visibility::Hidden
    }
}
