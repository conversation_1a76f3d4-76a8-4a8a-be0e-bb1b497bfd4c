# Changelog

All notable changes to the PianoRhythm project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- **Initialization System Architecture**: Complete refactoring of app startup process to eliminate race conditions
  - New `InitializationService` with dependency-based state machine
  - Comprehensive initialization types and step definitions
  - Retry mechanisms and timeout handling for robust startup
  - Progress tracking and detailed error reporting
  - Full test coverage with 11 unit tests
- **Technical Documentation**:
  - `docs/index.md` - Comprehensive technical documentation
  - `docs/initialization-quick-reference.mdx` - Developer quick reference guide
  - Updated README.md with initialization system overview

### Changed
- **Audio Service Initialization Order**: Refactored soundfont loading to occur after audio service initialization
  - Audio service now initializes before soundfont loading (prevents audio context issues)
  - Soundfont loading moved to dedicated initialization step with proper validation
  - App settings and other services now depend on soundfont being loaded
  - Updated dependency chain: AudioService → ClientAddedToSynth → Soundfont → AppSettings
  - Enhanced error handling for soundfont loading failures with fallback to default soundfont

### Fixed
- **Race Condition Elimination**: Resolved "Synth engine created but client socket ID not set, yet" error
  - Proper sequencing of synth engine initialization and client socket ID assignment
  - Enhanced audio service initialization with dependency validation
  - Improved core middleware error handling and logging
- **Startup Reliability**: Eliminated timing-dependent initialization failures
  - Guaranteed order of WebSocket connection, client loading, and audio initialization
  - Robust handling of network delays and resource loading issues
  - Better error recovery for transient failures

### Changed
- **App Loading Process**: Refactored `src/routes/app-loading.tsx` to use initialization service
  - Sequential step execution with proper dependency management
  - Enhanced progress reporting and user feedback
  - Improved error handling and recovery mechanisms
- **Audio Service**: Enhanced `src/services/audio.service.ts` for better synchronization
  - Wait for both client loaded AND socket ID before adding client to synth
  - Improved worklet initialization with proper dependency checks
  - Enhanced logging for debugging race conditions
- **Core Middleware**: Updated Rust core middleware for better error handling
  - Changed error to warning for race condition scenarios
  - Enhanced logging for AddClient actions
  - More informative debug messages

### Technical Details
- **Dependencies**: Clear dependency graph ensures proper initialization order
- **Error Recovery**: Configurable retry attempts with exponential backoff
- **Timeout Protection**: Prevents hanging operations with configurable timeouts
- **Progress Tracking**: Real-time initialization progress with detailed status updates
- **Testing**: Comprehensive unit test suite with mock-based testing patterns

### Migration Guide
- Existing initialization code continues to work
- New features should use the `InitializationService` for startup operations
- See `docs/index.md` for detailed migration instructions

---

## Previous Versions

*Note: This changelog was created as part of the initialization system refactoring. Previous version history may be available in git commit history.*
